"""
    calculer_formule10B_block_cumulative_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AVEC DÉPENDANCES - BlockT (PRIORITÉ 5)
FORMULE 10B : Entropie de Bloc Vraie (VERSION CORRIGÉE SELON DOCUMENTATION)
Calcule la vraie entropie jointe selon la règle de chaîne généralisée.
Formule : BlockT_n = H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)
Basée sur la règle de chaîne : p(x₁,...,xₙ) = p(x₁) × ∏ᵢ₌₂ⁿ p(xᵢ|x₁,...,xᵢ₋₁)
DÉPENDANCES : calculer_probabilite_conditionnelle_theo (Module Support Probabilités)
"""
function calculer_formule10B_block_cumulative_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Calculer l'entropie jointe selon la règle de chaîne généralisée :
    # H(X₁,...,Xₙ) = ∑ᵢ₌₁ⁿ H(Xᵢ|X₁,...,Xᵢ₋₁)

    total_joint_entropy = zero(T)

    for i in 1:n
        if i == 1
            # H(X₁) = -log₂(p_theo(x₁))
            first_value = sequence[1]
            p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
            if p_first > zero(T)
                total_joint_entropy += -(log(p_first) / log(formulas.base))
            end
        else
            # H(Xᵢ|X₁,...,Xᵢ₋₁) = -log₂(p_theo(xᵢ|x₁,...,xᵢ₋₁))
            current_value = sequence[i]
            context = sequence[1:i-1]

            # Calculer la vraie probabilité conditionnelle p_theo(xᵢ|x₁,...,xᵢ₋₁)
            p_conditional = calculer_probabilite_conditionnelle_theo(
                formulas, current_value, context
            )

            if p_conditional > zero(T)
                total_joint_entropy += -(log(p_conditional) / log(formulas.base))
            end
        end
    end

    return total_joint_entropy
end
