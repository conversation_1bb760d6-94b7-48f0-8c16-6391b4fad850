#!/usr/bin/env python3
"""
EXTRACTEUR DE MÉTHODES EXTÉRIEURES POUR LES 8 MÉTRIQUES DE PREDICTEUR_INDEX5.JL
==============================================================================

Script Python qui identifie et extrait toutes les méthodes extérieures nécessaires 
à chaque métrique de predicteur_index5.jl depuis entropie_baccarat_analyzer.jl.

Basé sur l'analyse des 8 métriques :
1. CondT (Entropie Conditionnelle) - PRIORITÉ 1
2. DivKLT (Divergence KL) - PRIORITÉ 2  
3. CrossT (Entropie Croisée) - PRIORITÉ 2
4. MetricT (Entropie Métrique) - PRIORITÉ 3
5. TopoT (Entropie Topologique) - PRIORITÉ 3
6. TauxT (Taux d'Entropie) - PRIORITÉ 4
7. Shannon<PERSON> (Entropie de Shannon) - PRIORITÉ 5
8. BlockT (Entropie de Bloc) - PRIORITÉ 5
"""

import re
import os
import sys
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

@dataclass
class MetriqueInfo:
    """Information sur une métrique et ses dépendances"""
    nom: str
    priorite: int
    fonction_principale: str
    fonctions_internes: List[str]
    fonctions_externes_requises: List[str]
    description: str

@dataclass
class FunctionExtraite:
    """Information sur une fonction extraite"""
    nom: str
    signature: str
    docstring: str
    code_complet: str
    ligne_debut: int
    ligne_fin: int
    dependances: Set[str]

class ExtracteurMethodesMetriques:
    """Extracteur spécialisé pour les méthodes des 8 métriques"""
    
    def __init__(self, fichier_analyzer: str = "autres/entropie_baccarat_analyzer.jl"):
        self.fichier_analyzer = fichier_analyzer
        self.contenu_analyzer = ""
        self.metriques_info = {}
        self.fonctions_extraites = {}
        self.fonctions_manquantes = set()
        
        # Définition des 8 métriques et leurs dépendances
        self._definir_metriques()
    
    def _definir_metriques(self):
        """Définit les 8 métriques et leurs dépendances extérieures"""
        
        self.metriques_info = {
            "CondT": MetriqueInfo(
                nom="CondT",
                priorite=1,
                fonction_principale="calculer_formule5B_conditionnelle_theo",
                fonctions_internes=["calculer_entropie_conditionnelle"],
                fonctions_externes_requises=[
                    "trouver_fichier_json_recent",
                    "charger_donnees_partie", 
                    "compter_parties_disponibles",
                    "load_baccarat_data",
                    "parse_main_data"
                ],
                description="Entropie Conditionnelle Cumulative - Prévisibilité globale du système INDEX5"
            ),
            
            "DivKLT": MetriqueInfo(
                nom="DivKLT",
                priorite=2,
                fonction_principale="calculer_formule6B_divergence_kl_theo",
                fonctions_internes=[],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "extract_index_values"
                ],
                description="Divergence KL Observée vs Théorique - Écart entre fréquences observées et probabilités théoriques"
            ),
            
            "CrossT": MetriqueInfo(
                nom="CrossT", 
                priorite=2,
                fonction_principale="calculer_formule8B_entropie_croisee_theo",
                fonctions_internes=[],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "extract_index_values"
                ],
                description="Entropie Croisée Observée vs Théorique - Coût d'encodage avec probabilités théoriques"
            ),
            
            "MetricT": MetriqueInfo(
                nom="MetricT",
                priorite=3,
                fonction_principale="calculer_formule4B_entropie_metrique_theo",
                fonctions_internes=["calculer_entropie_conditionnelle"],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "calculate_shannon_entropy"
                ],
                description="Entropie Métrique Kolmogorov-Sinai - Impact de l'ajout du n-ème élément"
            ),
            
            "TopoT": MetriqueInfo(
                nom="TopoT",
                priorite=3,
                fonction_principale="calculer_formule9B_entropie_topologique_theo",
                fonctions_internes=["calculer_entropie_blocs"],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "calculate_block_entropy"
                ],
                description="Entropie Topologique Multi-Échelles - Complexité à 3 niveaux de résolution"
            ),
            
            "TauxT": MetriqueInfo(
                nom="TauxT",
                priorite=4,
                fonction_principale="calculer_formule3B_taux_entropie_theo",
                fonctions_internes=[],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data"
                ],
                description="Taux d'Entropie - Complexité normalisée (relation directe avec BlockT)"
            ),
            
            "ShannonT": MetriqueInfo(
                nom="ShannonT",
                priorite=5,
                fonction_principale="calculer_formule1B_shannon_jointe_theo",
                fonctions_internes=[],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "calculate_shannon_entropy",
                    "calculate_joint_entropy"
                ],
                description="Entropie de Shannon Jointe - Diversité observée avec probabilités théoriques"
            ),
            
            "BlockT": MetriqueInfo(
                nom="BlockT",
                priorite=5,
                fonction_principale="calculer_formule10B_block_cumulative_theo",
                fonctions_internes=[],
                fonctions_externes_requises=[
                    "load_baccarat_data",
                    "parse_main_data",
                    "calculate_conditional_probability",
                    "calculate_joint_probability",
                    "calculate_empirical_probability",
                    "extract_sequence_data",
                    "validate_sequence_data"
                ],
                description="Entropie de Bloc Vraie - Complexité totale selon la règle de chaîne généralisée"
            )
        }
    
    def charger_fichier_analyzer(self):
        """Charge le contenu du fichier entropie_baccarat_analyzer.jl"""
        try:
            with open(self.fichier_analyzer, 'r', encoding='utf-8') as f:
                self.contenu_analyzer = f.read()
            print(f"✅ Fichier analyzer chargé : {self.fichier_analyzer}")
            print(f"📊 Taille du fichier : {len(self.contenu_analyzer)} caractères")
            
        except FileNotFoundError:
            print(f"❌ Erreur : Fichier non trouvé - {self.fichier_analyzer}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            sys.exit(1)
    
    def extraire_fonction_complete(self, nom_fonction: str) -> FunctionExtraite:
        """Extrait une fonction complète avec sa documentation"""
        
        # Patterns pour trouver la fonction
        patterns = [
            rf'"""[^"]*{re.escape(nom_fonction)}[^"]*""".*?function\s+{re.escape(nom_fonction)}\s*\([^)]*\).*?(?=\n\s*(?:function|struct|end\s*$|\Z))',
            rf'function\s+{re.escape(nom_fonction)}\s*\([^)]*\).*?(?=\n\s*(?:function|struct|end\s*$|\Z))',
            rf'{re.escape(nom_fonction)}\s*\([^)]*\)\s*=.*?(?=\n\w|\Z)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, self.contenu_analyzer, re.DOTALL | re.MULTILINE)
            if match:
                break
        
        if not match:
            return None
        
        code_complet = match.group(0)
        
        # Extraire la signature
        sig_match = re.search(rf'function\s+({re.escape(nom_fonction)}\s*\([^)]*\))', code_complet)
        if not sig_match:
            sig_match = re.search(rf'({re.escape(nom_fonction)}\s*\([^)]*\))', code_complet)
        signature = sig_match.group(1).strip() if sig_match else nom_fonction
        
        # Extraire la docstring
        doc_match = re.search(r'"""(.*?)"""', code_complet, re.DOTALL)
        docstring = doc_match.group(1).strip() if doc_match else ""
        
        # Calculer les numéros de ligne
        lignes_avant = self.contenu_analyzer[:match.start()].count('\n')
        lignes_fonction = code_complet.count('\n')
        
        # Identifier les dépendances
        dependances = self._identifier_dependances(code_complet)
        
        return FunctionExtraite(
            nom=nom_fonction,
            signature=signature,
            docstring=docstring,
            code_complet=code_complet,
            ligne_debut=lignes_avant + 1,
            ligne_fin=lignes_avant + lignes_fonction + 1,
            dependances=dependances
        )
    
    def _identifier_dependances(self, code: str) -> Set[str]:
        """Identifie les dépendances d'une fonction"""
        dependances = set()
        
        # Patterns pour identifier les appels
        patterns = [
            r'(\w+)\s*\(',  # Appels de fonctions
            r'(\w+)\.',     # Accès modules/propriétés
            r'using\s+(\w+)',  # Imports
            r'import\s+(\w+)'  # Imports
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, code)
            for match in matches:
                if (match and 
                    not match.startswith('_') and 
                    match not in ['if', 'for', 'while', 'try', 'catch', 'end', 'function', 'struct', 'return'] and
                    len(match) > 2):
                    dependances.add(match)
        
        return dependances
    
    def extraire_fonctions_pour_metrique(self, nom_metrique: str):
        """Extrait toutes les fonctions nécessaires pour une métrique"""
        if nom_metrique not in self.metriques_info:
            print(f"❌ Métrique inconnue : {nom_metrique}")
            return
        
        metrique = self.metriques_info[nom_metrique]
        print(f"\n🔍 EXTRACTION POUR {nom_metrique} (PRIORITÉ {metrique.priorite})")
        print(f"📝 {metrique.description}")
        print("-" * 80)
        
        fonctions_extraites_metrique = {}
        
        # Extraire les fonctions externes requises
        for nom_fonction in metrique.fonctions_externes_requises:
            print(f"\n🔍 Recherche de : {nom_fonction}")
            
            fonction = self.extraire_fonction_complete(nom_fonction)
            
            if fonction:
                fonctions_extraites_metrique[nom_fonction] = fonction
                print(f"   ✅ Trouvée (lignes {fonction.ligne_debut}-{fonction.ligne_fin})")
                if fonction.dependances:
                    print(f"   📋 Dépendances : {', '.join(sorted(fonction.dependances))}")
            else:
                self.fonctions_manquantes.add(nom_fonction)
                print(f"   ❌ Non trouvée")
        
        self.fonctions_extraites[nom_metrique] = fonctions_extraites_metrique
        
        print(f"\n📊 Résumé {nom_metrique} :")
        print(f"   • Fonctions requises : {len(metrique.fonctions_externes_requises)}")
        print(f"   • Fonctions extraites : {len(fonctions_extraites_metrique)}")
        print(f"   • Taux de réussite : {len(fonctions_extraites_metrique)/len(metrique.fonctions_externes_requises)*100:.1f}%")
    
    def extraire_toutes_metriques(self):
        """Extrait les fonctions pour toutes les 8 métriques"""
        print("🚀 EXTRACTION DES MÉTHODES POUR LES 8 MÉTRIQUES")
        print("=" * 80)
        
        # Trier par priorité
        metriques_triees = sorted(self.metriques_info.items(), key=lambda x: x[1].priorite)
        
        for nom_metrique, _ in metriques_triees:
            self.extraire_fonctions_pour_metrique(nom_metrique)
    
    def generer_rapport_complet(self, fichier_sortie: str):
        """Génère un rapport complet de l'extraction par métrique"""
        print(f"\n📄 GÉNÉRATION DU RAPPORT COMPLET : {fichier_sortie}")
        
        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("# RAPPORT D'EXTRACTION DES MÉTHODES PAR MÉTRIQUE\n")
            f.write("=" * 80 + "\n\n")
            f.write("Extraction des méthodes extérieures nécessaires aux 8 métriques de predicteur_index5.jl\n\n")
            
            # Statistiques globales
            total_requises = sum(len(m.fonctions_externes_requises) for m in self.metriques_info.values())
            total_extraites = sum(len(foncs) for foncs in self.fonctions_extraites.values())
            
            f.write("## STATISTIQUES GLOBALES\n")
            f.write(f"- Métriques analysées : {len(self.metriques_info)}\n")
            f.write(f"- Fonctions requises au total : {total_requises}\n")
            f.write(f"- Fonctions extraites avec succès : {total_extraites}\n")
            f.write(f"- Fonctions manquantes : {len(self.fonctions_manquantes)}\n")
            f.write(f"- Taux de réussite global : {total_extraites/total_requises*100:.1f}%\n\n")
            
            # Détail par métrique
            f.write("## DÉTAIL PAR MÉTRIQUE\n\n")
            
            metriques_triees = sorted(self.metriques_info.items(), key=lambda x: x[1].priorite)
            
            for nom_metrique, metrique_info in metriques_triees:
                f.write(f"### {nom_metrique} - PRIORITÉ {metrique_info.priorite}\n\n")
                f.write(f"**Description :** {metrique_info.description}\n\n")
                f.write(f"**Fonction principale :** `{metrique_info.fonction_principale}`\n\n")
                
                if metrique_info.fonctions_internes:
                    f.write(f"**Fonctions internes :** {', '.join(metrique_info.fonctions_internes)}\n\n")
                
                fonctions_metrique = self.fonctions_extraites.get(nom_metrique, {})
                f.write(f"**Fonctions externes extraites :** {len(fonctions_metrique)}/{len(metrique_info.fonctions_externes_requises)}\n\n")
                
                # Détail des fonctions extraites
                for nom_fonction, fonction in fonctions_metrique.items():
                    f.write(f"#### {nom_fonction}\n")
                    f.write(f"**Signature :** `{fonction.signature}`\n\n")
                    f.write(f"**Localisation :** Lignes {fonction.ligne_debut}-{fonction.ligne_fin}\n\n")
                    
                    if fonction.docstring:
                        f.write(f"**Documentation :**\n```\n{fonction.docstring}\n```\n\n")
                    
                    if fonction.dependances:
                        f.write(f"**Dépendances :** {', '.join(sorted(fonction.dependances))}\n\n")
                    
                    f.write(f"**Code complet :**\n```julia\n{fonction.code_complet}\n```\n\n")
                
                f.write("-" * 80 + "\n\n")
            
            # Fonctions manquantes
            if self.fonctions_manquantes:
                f.write("## FONCTIONS MANQUANTES\n\n")
                for fonction in sorted(self.fonctions_manquantes):
                    f.write(f"- {fonction}\n")
                f.write("\n")
        
        print(f"✅ Rapport généré avec succès")
    
    def executer_extraction_complete(self):
        """Exécute le processus complet d'extraction"""
        print("🎯 EXTRACTEUR DE MÉTHODES POUR LES 8 MÉTRIQUES DE PREDICTEUR_INDEX5.JL")
        print("=" * 80)
        
        # 1. Charger le fichier analyzer
        self.charger_fichier_analyzer()
        
        # 2. Extraire pour toutes les métriques
        self.extraire_toutes_metriques()
        
        # 3. Générer le rapport
        fichier_rapport = "rapport_extraction_metriques.md"
        self.generer_rapport_complet(fichier_rapport)
        
        print(f"\n✅ EXTRACTION TERMINÉE")
        print(f"📄 Rapport disponible : {fichier_rapport}")

def main():
    """Fonction principale"""
    fichier_analyzer = "autres/entropie_baccarat_analyzer.jl"
    
    if not os.path.exists(fichier_analyzer):
        print(f"❌ Fichier non trouvé : {fichier_analyzer}")
        sys.exit(1)
    
    extracteur = ExtracteurMethodesMetriques(fichier_analyzer)
    extracteur.executer_extraction_complete()

if __name__ == "__main__":
    main()
