"""
PRÉDICTEUR INDEX5 - VERSION RESTRUCTURÉE PROFESSIONNELLE
========================================================

Structure optimale basée sur l'analyse des dépendances des 8 métriques.
Architecture modulaire avec séparation claire des responsabilités.

ARCHITECTURE MODULAIRE :
- MODULE 1: Types et Structures de Base
- MODULE 2: Métriques Autonomes (Niveau 1 - Aucune dépendance)
- MODULE 3: Support Probabilités (Niveau 2)
- MODULE 4: Métriques avec Dépendances (Niveau 3)
- MODULE 5: Métriques Complexes (Niveau 4)
- MODULE 6: Orchestrateur
- MODULE 7: Utilitaires et Interface

MÉTRIQUES PAR NIVEAU DE DÉPENDANCE :
NIVEAU 1 (Autonomes): DivKLT, CrossT, ShannonT, TopoT
NIVEAU 2 (Support): Fonctions de probabilités
NIVEAU 3 (Dépendantes): CondT, MetricT, BlockT
NIVEAU 4 (Complexes): TauxT
"""

using JSON
using Printf
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 1: TYPES ET STRUCTURES DE BASE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData

Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    FormulasTheoretical{T<:AbstractFloat}

Structure contenant les probabilités théoriques INDEX5 et paramètres pour les calculs d'entropie.
"""
mutable struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    sequence_complete::Vector{String}  # Séquence complète pour calculs empiriques

    function FormulasTheoretical{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        # Probabilités théoriques INDEX5 (identiques à entropie_baccarat_analyzer.jl)
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        new{T}(base, epsilon, theoretical_probs, String[])
    end
end

# Constructeur de convenance
FormulasTheoretical(base::Real = 2.0, epsilon::Real = 1e-12) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

"""
    MetriquesTheorique{T<:AbstractFloat}

Structure pour stocker les 10 métriques théoriques calculées.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
1. InfoMutT - Information Mutuelle (dépendance directe)
2. CondT - Entropie Conditionnelle (prévisibilité immédiate)
3. DivKLT - Divergence KL (biais du modèle)
4. CrossT - Entropie Croisée (inefficacité du modèle)
5. MetricT - Entropie Métrique (variation de complexité)
6. TopoT - Entropie Topologique (patterns multi-échelles)
7. TauxT - Taux d'Entropie (complexité normalisée)
8. ShannonT - Entropie de Shannon (diversité observée)
9. BlockT - Entropie Jointe (complexité totale)
"""
struct MetriquesTheorique{T<:AbstractFloat}
    cond_t::T         # 1. CondT - PRIORITÉ 1
    divkl_t::T        # 2. DivKLT - PRIORITÉ 2
    cross_t::T        # 3. CrossT - PRIORITÉ 2
    metric_t::T       # 4. MetricT - PRIORITÉ 3
    topo_t::T         # 5. TopoT - PRIORITÉ 3
    taux_t::T         # 6. TauxT - PRIORITÉ 4
    shannon_t::T      # 7. ShannonT - PRIORITÉ 5
    block_t::T        # 8. BlockT - PRIORITÉ 5
end

"""
    DifferentielsPredictifs{T<:AbstractFloat}

Structure pour stocker les différentiels des 10 métriques théoriques pour la prédiction.
Calcule |métrique(n+1) - métrique(n)| pour chacune des 6 possibilités à la main n+1.
ORDRE DE PRIORITÉ PRÉDICTIVE (du plus important au moins important) :
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    # Différentiels des 8 métriques théoriques (ordre de priorité prédictive)
    diff_cond_t::T         # 1. |CondT(n+1) - CondT(n)| - PRIORITÉ 1
    diff_divkl_t::T        # 2. |DivKLT(n+1) - DivKLT(n)| - PRIORITÉ 2
    diff_cross_t::T        # 3. |CrossT(n+1) - CrossT(n)| - PRIORITÉ 2
    diff_metric_t::T       # 4. |MetricT(n+1) - MetricT(n)| - PRIORITÉ 3
    diff_topo_t::T         # 5. |TopoT(n+1) - TopoT(n)| - PRIORITÉ 3
    diff_taux_t::T         # 6. |TauxT(n+1) - TauxT(n)| - PRIORITÉ 4
    diff_shannon_t::T      # 7. |ShannonT(n+1) - ShannonT(n)| - PRIORITÉ 5
    diff_block_t::T        # 8. |BlockT(n+1) - BlockT(n)| - PRIORITÉ 5
end

"""
    CalculateurDifferentielsPredictifs{T<:AbstractFloat}

Classe pour calculer les différentiels prédictifs des métriques théoriques.
"""
struct CalculateurDifferentielsPredictifs{T<:AbstractFloat}
    formulas::FormulasTheoretical{T}

    function CalculateurDifferentielsPredictifs{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        formulas = FormulasTheoretical{T}(base, epsilon)
        new{T}(formulas)
    end
end

# Constructeur de convenance
CalculateurDifferentielsPredictifs(base::Real = 2.0, epsilon::Real = 1e-12) =
    CalculateurDifferentielsPredictifs{Float64}(Float64(base), Float64(epsilon))

"""
    PredictionResult

Structure pour stocker le résultat de prédiction pour une main avec métriques et différentiels.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}  # INDEX5 réellement observé à la main n+1
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════════════════

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 7: UTILITAIRES ET INTERFACE
# ═══════════════════════════════════════════════════════════════════════════════
# Fonctions utilitaires pour le chargement de données et l'interface :
# - trouver_fichier_json_recent (utilitaire fichiers)
# - charger_donnees_partie (chargement données)
# - compter_parties_disponibles (utilitaire comptage)
# - Fonctions d'affichage et d'export

"""
    trouver_fichier_json_recent(dossier::String) -> String

UTILITAIRE - Gestion des fichiers
Trouve le fichier JSON le plus récent dans le dossier spécifié.
"""
function trouver_fichier_json_recent(dossier::String)
    if !isdir(dossier)
        throw(ArgumentError("Le dossier '$dossier' n'existe pas"))
    end
    
    fichiers_json = filter(f -> endswith(f, ".json"), readdir(dossier))
    
    if isempty(fichiers_json)
        throw(ArgumentError("Aucun fichier JSON trouvé dans le dossier '$dossier'"))
    end
    
    # Trier par date de modification (plus récent en premier)
    fichiers_avec_dates = [(f, stat(joinpath(dossier, f)).mtime) for f in fichiers_json]
    sort!(fichiers_avec_dates, by=x->x[2], rev=true)
    
    fichier_recent = fichiers_avec_dates[1][1]
    chemin_complet = joinpath(dossier, fichier_recent)
    
    println("📁 Fichier JSON le plus récent trouvé : $fichier_recent")
    return chemin_complet
end

"""
    charger_donnees_partie(chemin_fichier::String, numero_partie::Int) -> Vector{MainData}

Charge les données d'une partie spécifique depuis un fichier JSON.
"""
function charger_donnees_partie(chemin_fichier::String, numero_partie::Int)
    println("📖 Chargement du fichier : $chemin_fichier")
    println("🎯 Recherche de la partie numéro : $numero_partie")

    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees") || isempty(donnees["parties_condensees"])
            throw(ArgumentError("Format de fichier invalide : 'parties_condensees' manquant ou vide"))
        end

        # Chercher la partie demandée
        partie_trouvee = nothing
        for partie in donnees["parties_condensees"]
            if partie["partie_number"] == numero_partie
                partie_trouvee = partie
                break
            end
        end

        if partie_trouvee === nothing
            throw(ArgumentError("Partie numéro $numero_partie non trouvée"))
        end

        mains_data = partie_trouvee["mains_condensees"]

        # Convertir en structures MainData
        mains = MainData[]

        for main_data in mains_data
            # Ignorer les mains avec des données manquantes
            if main_data["main_number"] === nothing ||
               main_data["index1"] === "" ||
               main_data["index2"] === "" ||
               main_data["index3"] === ""
                continue
            end

            push!(mains, MainData(
                main_data["main_number"],
                main_data["manche_pb_number"],
                main_data["index1"],
                main_data["index2"],
                main_data["index3"],
                main_data["index5"]
            ))
        end

        println("✅ Partie $numero_partie chargée : $(length(mains)) mains valides")
        println("📊 Statistiques de la partie :")
        if haskey(partie_trouvee, "statistiques")
            stats = partie_trouvee["statistiques"]
            println("   • Total mains : $(stats["total_mains"])")
            println("   • Manches P/B : $(stats["total_manches_pb"])")
            println("   • Ties : $(stats["total_ties"])")
        end

        return mains

    catch e
        println("❌ Erreur lors du chargement : $e")
        rethrow(e)
    end
end

"""
    compter_parties_disponibles(chemin_fichier::String) -> Int

Compte le nombre de parties disponibles dans le fichier JSON.
"""
function compter_parties_disponibles(chemin_fichier::String)
    try
        contenu = read(chemin_fichier, String)
        donnees = JSON.parse(contenu)

        if !haskey(donnees, "parties_condensees")
            return 0
        end

        return length(donnees["parties_condensees"])

    catch e
        println("❌ Erreur lors du comptage des parties : $e")
        return 0
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 2: MÉTRIQUES AUTONOMES (NIVEAU 1 - AUCUNE DÉPENDANCE)
# ═══════════════════════════════════════════════════════════════════════════════
# Métriques qui ne dépendent d'aucune autre fonction interne :
# - ShannonT (calculer_formule1B_shannon_jointe_theo)
# - DivKLT (calculer_formule6B_divergence_kl_theo)
# - CrossT (calculer_formule8B_entropie_croisee_theo)
# - TopoT (calculer_formule9B_entropie_topologique_theo)

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - ShannonT (PRIORITÉ 5)
FORMULE 1B : Entropie de Shannon Jointe (VERSION THÉORIQUE)
Calcule l'entropie jointe théorique pour la fenêtre croissante de la main 1 à la main n.
DÉPENDANCES : AUCUNE
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)
    total = length(subsequence)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p(x) log₂(p(x))
            # Formule correcte selon cours_entropie/niveau_debutant/02_formule_shannon.md
            # Caractères : H = -∑ pᵢ × log₂(pᵢ)
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end



# MÉTRIQUE COMPLEXE - TauxT sera définie après BlockT pour respecter les dépendances

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 3: SUPPORT PROBABILITÉS (NIVEAU 2)
# ═══════════════════════════════════════════════════════════════════════════════
# Fonctions de support pour les calculs de probabilités :
# - calculer_probabilite_conditionnelle_empirique (base)
# - calculer_probabilite_jointe_theo (dépend de empirique)
# - calculer_probabilite_conditionnelle_theo (dépend de jointe)

"""
    calculer_probabilite_conditionnelle_theo(formulas::FormulasTheoretical{T}, current_value::String, context::Vector{String}) where T -> T

SUPPORT PROBABILITÉS - Niveau 3
Calcule la vraie probabilité conditionnelle théorique p_theo(xᵢ|x₁,...,xᵢ₋₁)
Sans hypothèse d'indépendance - utilise les probabilités jointes théoriques.
Formule : p(xᵢ|x₁,...,xᵢ₋₁) = p(x₁,...,xᵢ) / p(x₁,...,xᵢ₋₁)
DÉPENDANCES : calculer_probabilite_jointe_theo
"""
function calculer_probabilite_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    current_value::String,
    context::Vector{String}
) where T<:AbstractFloat

    # Construire la séquence complète : context + current_value
    full_sequence = vcat(context, [current_value])

    # Calculer p_theo(x₁,...,xᵢ) - probabilité jointe de la séquence complète
    p_joint_full = calculer_probabilite_jointe_theo(formulas, full_sequence)

    # Calculer p_theo(x₁,...,xᵢ₋₁) - probabilité jointe du contexte
    p_joint_context = calculer_probabilite_jointe_theo(formulas, context)

    # p_theo(xᵢ|x₁,...,xᵢ₋₁) = p_theo(x₁,...,xᵢ) / p_theo(x₁,...,xᵢ₋₁)
    if p_joint_context > zero(T)
        return p_joint_full / p_joint_context
    else
        # Si le contexte a une probabilité nulle, utiliser la probabilité marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

"""
    calculer_probabilite_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}) where T -> T

SUPPORT PROBABILITÉS - Niveau 2
Calcule la probabilité jointe théorique d'une séquence en utilisant les probabilités conditionnelles
empiriques basées sur les observations de la séquence complète.
Pour une séquence de longueur 1, retourne p_theo(x₁).
DÉPENDANCES : calculer_probabilite_conditionnelle_empirique
Pour une séquence plus longue, utilise la règle de chaîne avec les probabilités conditionnelles empiriques.
"""
function calculer_probabilite_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String}
) where T<:AbstractFloat

    if isempty(sequence)
        return zero(T)
    end

    if length(sequence) == 1
        # p_theo(x₁) - utiliser la probabilité théorique
        return get(formulas.theoretical_probs, sequence[1], formulas.epsilon)
    end

    # Pour les séquences plus longues, utiliser les probabilités conditionnelles empiriques
    # basées sur les observations dans la séquence complète disponible

    joint_prob = one(T)

    # Premier terme : p_theo(x₁) - probabilité théorique du premier élément
    first_value = sequence[1]
    p_first = get(formulas.theoretical_probs, first_value, formulas.epsilon)
    joint_prob *= p_first

    # Termes suivants : p_empirique(xᵢ|xᵢ₋₁) basé sur les observations
    for i in 2:length(sequence)
        current_value = sequence[i]
        previous_value = sequence[i-1]

        # Calculer p_empirique(xᵢ|xᵢ₋₁) basé sur les observations dans la séquence complète
        p_conditional_empirique = calculer_probabilite_conditionnelle_empirique(
            formulas, current_value, previous_value
        )

        joint_prob *= p_conditional_empirique
    end

    return joint_prob
end

"""
    calculer_probabilite_conditionnelle_empirique(formulas::FormulasTheoretical{T}, current_value::String, previous_value::String) where T -> T

SUPPORT PROBABILITÉS - Niveau 1 (Base)
Calcule la probabilité conditionnelle empirique p_empirique(xᵢ|xᵢ₋₁) basée sur les observations
dans la séquence complète disponible dans formulas.
Formule : p_empirique(xᵢ|xᵢ₋₁) = count(xᵢ₋₁ → xᵢ) / count(xᵢ₋₁)
DÉPENDANCES : AUCUNE
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    # Accéder à la séquence complète stockée dans formulas
    # Si la séquence complète n'est pas disponible, utiliser une approximation
    if !hasfield(typeof(formulas), :sequence_complete) || isempty(formulas.sequence_complete)
        # Fallback : utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete

    # Compter les transitions previous_value → current_value
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    # Calculer p_empirique(current|previous) = count(previous → current) / count(previous)
    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        # Si previous_value n'a jamais été observé, utiliser la probabilité théorique marginale
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 4: MÉTRIQUES AVEC DÉPENDANCES (NIVEAU 3)
# ═══════════════════════════════════════════════════════════════════════════════
# Métriques qui dépendent d'autres fonctions internes :
# - MetricT (dépend de ShannonT)
# - CondT (dépend de ShannonT)
# - BlockT (dépend du module Support Probabilités)

"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AVEC DÉPENDANCES - MetricT (PRIORITÉ 3)
FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.
DÉPENDANCES : calculer_formule1B_shannon_jointe_theo (ShannonT)
Formule : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    return complexite_n - complexite_n_minus_1
end

"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AVEC DÉPENDANCES - CondT (PRIORITÉ 1)
FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Formule : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5
DÉPENDANCES : calculer_formule1B_shannon_jointe_theo (ShannonT)
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    return somme_entropies_conditionnelles / T(n)
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - DivKLT (PRIORITÉ 2)
FORMULE 6B : Divergence KL Observée vs Théorique (VERSION CORRIGÉE)
Mesure l'écart entre les fréquences réellement observées et les probabilités théoriques INDEX5.
Formule : DivKLT = ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "approximative" (modèle)
DÉPENDANCES : AUCUNE
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer la divergence KL : ∑ p_obs(x) × log₂(p_obs(x)/p_theo(x))
    divergence = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Divergence KL standard : p_obs × log₂(p_obs/p_theo)
            divergence += p_obs * (log(p_obs / p_theo) / log(formulas.base))
        end
    end

    return divergence
end



"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - CrossT (PRIORITÉ 2)
FORMULE 8B : Entropie Croisée Observée vs Théorique (VERSION CORRIGÉE)
Mesure le coût d'encodage des données réellement observées en utilisant les probabilités théoriques INDEX5 comme modèle de codage.
Formule : CrossT = -∑ p_obs(x) × log₂ p_theo(x)
Usage standard : p_obs = distribution "vraie" (observée), p_theo = distribution "de codage" (modèle)
DÉPENDANCES : AUCUNE
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie croisée : -∑ p_obs(x) × log₂ p_theo(x)
    cross_entropy = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        # Calculer p_obs(x) = count(x dans séquence [1:n]) / n
        p_obs = T(count) / n_total

        # Récupérer p_theo(x) depuis le modèle INDEX5
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            # Entropie croisée standard : -p_obs × log₂(p_theo)
            cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - TopoT (PRIORITÉ 3)
FORMULE 9B : Entropie Topologique Multi-Échelles (VERSION THÉORIQUE CORRIGÉE)
Entropie topologique multi-échelles basée sur la complexité théorique INDEX5 à 3 niveaux de résolution.
Formule : TopoT_n = 0.167 × H_theo(blocs_1) + 0.333 × H_theo(blocs_2) + 0.500 × H_theo(blocs_3)
Pondération basée sur la capacité informationnelle théorique de chaque échelle.
DÉPENDANCES : AUCUNE
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Fonction auxiliaire pour calculer H_theo(blocs_k) avec probabilités théoriques INDEX5
    function calculer_entropie_blocs(block_size::Int)
        if block_size > length(subsequence)
            return zero(T)
        end

        # Extraire tous les blocs de taille block_size
        blocs_distincts = Set{Vector{String}}()
        for i in 1:(length(subsequence) - block_size + 1)
            bloc = subsequence[i:i+block_size-1]
            push!(blocs_distincts, bloc)
        end

        # Calculer l'entropie théorique des blocs distincts
        entropy = zero(T)
        for bloc in blocs_distincts
            # Calculer la probabilité théorique du bloc sous hypothèse d'indépendance
            p_bloc_theo = one(T)
            for value in bloc
                p_value_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
                p_bloc_theo *= p_value_theo
            end

            if p_bloc_theo > zero(T)
                entropy -= p_bloc_theo * (log(p_bloc_theo) / log(formulas.base))
            end
        end

        return entropy
    end

    # Calculer les entropies pour chaque échelle
    h_blocs_1 = calculer_entropie_blocs(1)  # Valeurs individuelles
    h_blocs_2 = n >= 2 ? calculer_entropie_blocs(2) : zero(T)  # Paires consécutives
    h_blocs_3 = n >= 3 ? calculer_entropie_blocs(3) : zero(T)  # Triplets consécutifs

    # Poids théoriques basés sur la capacité informationnelle
    w1 = T(0.167)  # 16.7% pour complexité locale
    w2 = T(0.333)  # 33.3% pour complexité des transitions
    w3 = T(0.500)  # 50.0% pour complexité des motifs

    # Entropie topologique multi-échelles pondérée
    topo_entropy = w1 * h_blocs_1 + w2 * h_blocs_2 + w3 * h_blocs_3

    return topo_entropy
end
