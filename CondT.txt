"""
    calculer_formule5B_conditionnelle_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AVEC DÉPENDANCES - CondT (PRIORITÉ 1)
FORMULE 5B : Entropie Conditionnelle Cumulative (VERSION THÉORIQUE CORRIGÉE)
Entropie conditionnelle cumulative moyenne de toute la séquence [1:n] selon la Chain Rule.
Formule : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
Relation : CondT_n = H_theo(X₁,...,Xₙ) / n
Signification : Prévisibilité globale du système INDEX5
DÉPENDANCES : calculer_formule1B_shannon_jointe_theo (ShannonT)
"""
function calculer_formule5B_conditionnelle_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # Calculer la somme des entropies conditionnelles : ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_entropies_conditionnelles = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_entropies_conditionnelles += h_cond
    end

    # Retourner la moyenne : CondT_n = (1/n) × ∑ᵢ₌₁ⁿ H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    return somme_entropies_conditionnelles / T(n)
end
