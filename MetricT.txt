"""
    calculer_formule4B_entropie_metrique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AVEC DÉPENDANCES - MetricT (PRIORITÉ 3)
FORMULE 4B : Entropie Métrique Kolmogorov-Sinai (VERSION THÉORIQUE CORRIGÉE)
Entropie métrique pondérée : mesure l'impact de l'ajout du n-ème élément sur la complexité informationnelle pondérée globale.
DÉPENDANCES : calculer_formule1B_shannon_jointe_theo (ShannonT)
Formule : MetricT_n = [(2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)] - [(2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)]
"""
function calculer_formule4B_entropie_metrique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 1 || n > length(sequence)
        return zero(T)
    end

    # Fonction auxiliaire pour calculer H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    function calculer_entropie_conditionnelle(i::Int)
        if i == 1
            # H_theo(X₁) - pas de conditionnement
            value = sequence[i]
            p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
            return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
        else
            # H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
            h_i = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i)
            h_i_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, i-1)
            return h_i - h_i_minus_1
        end
    end

    # ÉTAPE 1 - Calcul pour la séquence [1:n-1]
    # Complexité_pondérée(n-1) = (2/((n-1)n)) × ∑ᵢ₌₁ⁿ⁻¹ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n_minus_1 = zero(T)
    for i in 1:(n-1)
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n_minus_1 += T(i) * h_cond
    end
    complexite_n_minus_1 = (T(2) / (T(n-1) * T(n))) * somme_ponderee_n_minus_1

    # ÉTAPE 2 - Calcul pour la séquence [1:n]
    # Complexité_pondérée(n) = (2/(n(n+1))) × ∑ᵢ₌₁ⁿ i × H_theo(Xᵢ | X₁,...,Xᵢ₋₁)
    somme_ponderee_n = zero(T)
    for i in 1:n
        h_cond = calculer_entropie_conditionnelle(i)
        somme_ponderee_n += T(i) * h_cond
    end
    complexite_n = (T(2) / (T(n) * T(n+1))) * somme_ponderee_n

    # ÉTAPE 3 - DIFFÉRENCE (ENTROPIE MÉTRIQUE PONDÉRÉE)
    # MetricT_n = Complexité_pondérée(n) - Complexité_pondérée(n-1)
    return complexite_n - complexite_n_minus_1
end
