#!/usr/bin/env python3
"""
EXTRACTEUR DE MÉTHODES POUR PREDICTEUR_INDEX5.JL
===============================================

Script Python qui extrait complètement chaque méthode extérieure nécessaire 
à predicteur_index5.jl et présente dans entropie_baccarat_analyzer.jl pour les calculs.

Analyse les dépendances et extrait les fonctions requises avec leur documentation complète.
"""

import re
import os
import sys
from typing import List, Dict, Set, Tuple
from dataclasses import dataclass
from pathlib import Path

@dataclass
class FunctionInfo:
    """Information sur une fonction extraite"""
    name: str
    signature: str
    docstring: str
    body: str
    start_line: int
    end_line: int
    dependencies: Set[str]

class ExtracteurMethodes:
    """Extracteur de méthodes Julia pour predicteur_index5.jl"""
    
    def __init__(self, fichier_predicteur: str, fichier_analyzer: str):
        self.fichier_predicteur = fichier_predicteur
        self.fichier_analyzer = fichier_analyzer
        self.fonctions_requises = set()
        self.fonctions_extraites = {}
        self.contenu_predicteur = ""
        self.contenu_analyzer = ""
        
    def charger_fichiers(self):
        """Charge le contenu des fichiers Julia"""
        try:
            with open(self.fichier_predicteur, 'r', encoding='utf-8') as f:
                self.contenu_predicteur = f.read()
            print(f"✅ Fichier predicteur chargé : {self.fichier_predicteur}")
            
            with open(self.fichier_analyzer, 'r', encoding='utf-8') as f:
                self.contenu_analyzer = f.read()
            print(f"✅ Fichier analyzer chargé : {self.fichier_analyzer}")
            
        except FileNotFoundError as e:
            print(f"❌ Erreur : Fichier non trouvé - {e}")
            sys.exit(1)
        except Exception as e:
            print(f"❌ Erreur lors du chargement : {e}")
            sys.exit(1)
    
    def identifier_fonctions_requises(self):
        """Identifie toutes les fonctions requises par predicteur_index5.jl"""
        print("\n🔍 IDENTIFICATION DES FONCTIONS REQUISES")
        print("=" * 60)
        
        # Fonctions explicitement appelées dans predicteur_index5.jl
        fonctions_appelees = [
            'trouver_fichier_json_recent',
            'charger_donnees_partie', 
            'compter_parties_disponibles',
            'load_baccarat_data',
            'parse_main_data',
            'extract_index_values',
            'validate_main_data'
        ]
        
        # Rechercher les appels de fonctions dans le code
        patterns_appels = [
            r'(\w+)\s*\(',  # Appels de fonctions génériques
            r'JSON\.(\w+)',  # Appels JSON
            r'(\w+)_data\(',  # Fonctions de données
            r'(\w+)_fichier\(',  # Fonctions de fichiers
        ]
        
        for pattern in patterns_appels:
            matches = re.findall(pattern, self.contenu_predicteur)
            for match in matches:
                if isinstance(match, tuple):
                    for m in match:
                        if m and len(m) > 2:
                            fonctions_appelees.append(m)
                else:
                    if match and len(match) > 2:
                        fonctions_appelees.append(match)
        
        # Nettoyer et filtrer les fonctions
        fonctions_filtrees = set()
        for func in fonctions_appelees:
            if (func and 
                not func.startswith('_') and 
                func not in ['if', 'for', 'while', 'try', 'catch', 'end', 'function', 'struct'] and
                len(func) > 2):
                fonctions_filtrees.add(func)
        
        self.fonctions_requises = fonctions_filtrees
        
        print(f"📊 Nombre de fonctions potentiellement requises : {len(self.fonctions_requises)}")
        for func in sorted(self.fonctions_requises):
            print(f"   • {func}")
    
    def extraire_fonction(self, nom_fonction: str) -> FunctionInfo:
        """Extrait une fonction complète du fichier analyzer"""
        
        # Pattern pour trouver la définition de fonction
        pattern_function = rf'function\s+{re.escape(nom_fonction)}\s*\([^)]*\).*?(?=\nfunction|\nstruct|\nend\s*$|\Z)'
        
        match = re.search(pattern_function, self.contenu_analyzer, re.DOTALL | re.MULTILINE)
        
        if not match:
            # Essayer avec un pattern plus flexible
            pattern_flexible = rf'{re.escape(nom_fonction)}\s*\([^)]*\).*?(?=\nfunction|\nstruct|\n\w+\s*=|\Z)'
            match = re.search(pattern_flexible, self.contenu_analyzer, re.DOTALL | re.MULTILINE)
        
        if not match:
            return None
            
        fonction_complete = match.group(0)
        
        # Extraire la signature
        signature_match = re.search(r'function\s+([^{]+)', fonction_complete)
        signature = signature_match.group(1).strip() if signature_match else nom_fonction
        
        # Extraire la docstring
        docstring_match = re.search(r'"""(.*?)"""', fonction_complete, re.DOTALL)
        docstring = docstring_match.group(1).strip() if docstring_match else ""
        
        # Calculer les numéros de ligne
        lignes_avant = self.contenu_analyzer[:match.start()].count('\n')
        lignes_fonction = fonction_complete.count('\n')
        
        # Identifier les dépendances
        dependencies = self._identifier_dependencies(fonction_complete)
        
        return FunctionInfo(
            name=nom_fonction,
            signature=signature,
            docstring=docstring,
            body=fonction_complete,
            start_line=lignes_avant + 1,
            end_line=lignes_avant + lignes_fonction + 1,
            dependencies=dependencies
        )
    
    def _identifier_dependencies(self, code_fonction: str) -> Set[str]:
        """Identifie les dépendances d'une fonction"""
        dependencies = set()
        
        # Patterns pour identifier les appels de fonctions
        patterns = [
            r'(\w+)\s*\(',  # Appels de fonctions
            r'(\w+)\.',     # Accès aux propriétés/modules
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, code_fonction)
            for match in matches:
                if (match and 
                    not match.startswith('_') and 
                    match not in ['if', 'for', 'while', 'try', 'catch', 'end', 'function'] and
                    len(match) > 2):
                    dependencies.add(match)
        
        return dependencies
    
    def extraire_toutes_fonctions(self):
        """Extrait toutes les fonctions requises"""
        print("\n📦 EXTRACTION DES FONCTIONS")
        print("=" * 60)
        
        fonctions_trouvees = 0
        fonctions_manquantes = []
        
        for nom_fonction in sorted(self.fonctions_requises):
            print(f"\n🔍 Recherche de : {nom_fonction}")
            
            fonction_info = self.extraire_fonction(nom_fonction)
            
            if fonction_info:
                self.fonctions_extraites[nom_fonction] = fonction_info
                fonctions_trouvees += 1
                print(f"   ✅ Trouvée (lignes {fonction_info.start_line}-{fonction_info.end_line})")
                if fonction_info.dependencies:
                    print(f"   📋 Dépendances : {', '.join(sorted(fonction_info.dependencies))}")
            else:
                fonctions_manquantes.append(nom_fonction)
                print(f"   ❌ Non trouvée")
        
        print(f"\n📊 RÉSUMÉ DE L'EXTRACTION")
        print(f"   • Fonctions trouvées : {fonctions_trouvees}")
        print(f"   • Fonctions manquantes : {len(fonctions_manquantes)}")
        
        if fonctions_manquantes:
            print(f"\n⚠️ Fonctions manquantes :")
            for func in fonctions_manquantes:
                print(f"   • {func}")
    
    def generer_rapport_extraction(self, fichier_sortie: str):
        """Génère un rapport complet de l'extraction"""
        print(f"\n📄 GÉNÉRATION DU RAPPORT : {fichier_sortie}")
        
        with open(fichier_sortie, 'w', encoding='utf-8') as f:
            f.write("# RAPPORT D'EXTRACTION DES MÉTHODES POUR PREDICTEUR_INDEX5.JL\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"Fichier source : {self.fichier_analyzer}\n")
            f.write(f"Fichier cible : {self.fichier_predicteur}\n")
            f.write(f"Date d'extraction : {self._get_timestamp()}\n\n")
            
            f.write(f"## STATISTIQUES\n")
            f.write(f"- Fonctions requises identifiées : {len(self.fonctions_requises)}\n")
            f.write(f"- Fonctions extraites avec succès : {len(self.fonctions_extraites)}\n")
            f.write(f"- Taux de réussite : {len(self.fonctions_extraites)/len(self.fonctions_requises)*100:.1f}%\n\n")
            
            # Détail des fonctions extraites
            f.write("## FONCTIONS EXTRAITES\n\n")
            
            for nom, info in sorted(self.fonctions_extraites.items()):
                f.write(f"### {nom}\n")
                f.write(f"**Signature :** `{info.signature}`\n\n")
                f.write(f"**Localisation :** Lignes {info.start_line}-{info.end_line}\n\n")
                
                if info.docstring:
                    f.write(f"**Documentation :**\n```\n{info.docstring}\n```\n\n")
                
                if info.dependencies:
                    f.write(f"**Dépendances :** {', '.join(sorted(info.dependencies))}\n\n")
                
                f.write(f"**Code complet :**\n```julia\n{info.body}\n```\n\n")
                f.write("-" * 80 + "\n\n")
        
        print(f"✅ Rapport généré avec succès")
    
    def _get_timestamp(self) -> str:
        """Retourne un timestamp formaté"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def executer_extraction_complete(self):
        """Exécute le processus complet d'extraction"""
        print("🚀 EXTRACTEUR DE MÉTHODES POUR PREDICTEUR_INDEX5.JL")
        print("=" * 80)
        
        # 1. Charger les fichiers
        self.charger_fichiers()
        
        # 2. Identifier les fonctions requises
        self.identifier_fonctions_requises()
        
        # 3. Extraire les fonctions
        self.extraire_toutes_fonctions()
        
        # 4. Générer le rapport
        fichier_rapport = "rapport_extraction_methodes.md"
        self.generer_rapport_extraction(fichier_rapport)
        
        print(f"\n✅ EXTRACTION TERMINÉE")
        print(f"📄 Rapport disponible : {fichier_rapport}")

def main():
    """Fonction principale"""
    # Chemins des fichiers
    fichier_predicteur = "predicteur_index5.jl"
    fichier_analyzer = "autres/entropie_baccarat_analyzer.jl"
    
    # Vérifier l'existence des fichiers
    if not os.path.exists(fichier_predicteur):
        print(f"❌ Fichier non trouvé : {fichier_predicteur}")
        sys.exit(1)
    
    if not os.path.exists(fichier_analyzer):
        print(f"❌ Fichier non trouvé : {fichier_analyzer}")
        sys.exit(1)
    
    # Créer l'extracteur et lancer l'extraction
    extracteur = ExtracteurMethodes(fichier_predicteur, fichier_analyzer)
    extracteur.executer_extraction_complete()

if __name__ == "__main__":
    main()
