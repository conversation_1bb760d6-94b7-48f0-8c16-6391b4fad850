"""
PRÉDICTEUR INDEX5 - VERSION RESTRUCTURÉE PROFESSIONNELLE
========================================================

Structure optimale basée sur l'analyse des dépendances des 8 métriques.
Architecture modulaire avec séparation claire des responsabilités.
"""

module PredicteurIndex5

using JSON
using Printf
using Dates

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 1: TYPES ET STRUCTURES DE BASE
# ═══════════════════════════════════════════════════════════════════════════════

"""
    MainData
Structure pour stocker les données d'une main.
"""
struct MainData
    main_number::Union{Int, Nothing}
    manche_pb_number::Union{Int, Nothing}
    index1::Union{Int, String}
    index2::String
    index3::String
    index5::String
end

"""
    FormulasTheoretical{T<:AbstractFloat}
Structure contenant les probabilités théoriques INDEX5 et paramètres pour les calculs d'entropie.
"""
mutable struct FormulasTheoretical{T<:AbstractFloat}
    base::T
    epsilon::T
    theoretical_probs::Dict{String,T}
    sequence_complete::Vector{String}

    function FormulasTheoretical{T}(base::T = T(2.0), epsilon::T = T(1e-12)) where T<:AbstractFloat
        theoretical_probs = Dict{String,T}(
            "0_A_BANKER" => T(0.085136), "1_A_BANKER" => T(0.086389),
            "0_B_BANKER" => T(0.064676), "1_B_BANKER" => T(0.065479),
            "0_C_BANKER" => T(0.077903), "1_C_BANKER" => T(0.078929),
            "0_A_PLAYER" => T(0.085240), "1_A_PLAYER" => T(0.086361),
            "0_B_PLAYER" => T(0.076907), "1_B_PLAYER" => T(0.077888),
            "0_C_PLAYER" => T(0.059617), "1_C_PLAYER" => T(0.060352),
            "0_A_TIE" => T(0.017719), "1_A_TIE" => T(0.017978),
            "0_B_TIE" => T(0.016281), "1_B_TIE" => T(0.016482),
            "0_C_TIE" => T(0.013241), "1_C_TIE" => T(0.013423)
        )
        new{T}(base, epsilon, theoretical_probs, String[])
    end
end

FormulasTheoretical(base::Real = 2.0, epsilon::Real = 1e-12) =
    FormulasTheoretical{Float64}(Float64(base), Float64(epsilon))

"""
    MetriquesTheorique{T<:AbstractFloat}
Structure pour stocker les 8 métriques théoriques calculées.
"""
struct MetriquesTheorique{T<:AbstractFloat}
    cond_t::T         # 1. CondT - PRIORITÉ 1
    divkl_t::T        # 2. DivKLT - PRIORITÉ 2
    cross_t::T        # 3. CrossT - PRIORITÉ 2
    metric_t::T       # 4. MetricT - PRIORITÉ 3
    topo_t::T         # 5. TopoT - PRIORITÉ 3
    taux_t::T         # 6. TauxT - PRIORITÉ 4
    shannon_t::T      # 7. ShannonT - PRIORITÉ 5
    block_t::T        # 8. BlockT - PRIORITÉ 5
end

"""
    DifferentielsPredictifs{T<:AbstractFloat}
Structure pour stocker les différentiels des 8 métriques théoriques.
"""
struct DifferentielsPredictifs{T<:AbstractFloat}
    diff_cond_t::T         # 1. |CondT(n+1) - CondT(n)| - PRIORITÉ 1
    diff_divkl_t::T        # 2. |DivKLT(n+1) - DivKLT(n)| - PRIORITÉ 2
    diff_cross_t::T        # 3. |CrossT(n+1) - CrossT(n)| - PRIORITÉ 2
    diff_metric_t::T       # 4. |MetricT(n+1) - MetricT(n)| - PRIORITÉ 3
    diff_topo_t::T         # 5. |TopoT(n+1) - TopoT(n)| - PRIORITÉ 3
    diff_taux_t::T         # 6. |TauxT(n+1) - TauxT(n)| - PRIORITÉ 4
    diff_shannon_t::T      # 7. |ShannonT(n+1) - ShannonT(n)| - PRIORITÉ 5
    diff_block_t::T        # 8. |BlockT(n+1) - BlockT(n)| - PRIORITÉ 5
end

"""
    PredictionResult
Structure pour stocker le résultat de prédiction pour une main.
"""
struct PredictionResult
    main_actuelle::Int
    index5_actuel::String
    index1_suivant::Int
    index5_observe::Union{String, Nothing}
    valeurs_possibles::Vector{String}
    metriques_par_possibilite::Vector{MetriquesTheorique{Float64}}
    differentiels_par_possibilite::Vector{DifferentielsPredictifs{Float64}}
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 2: MÉTRIQUES AUTONOMES (NIVEAU 1 - AUCUNE DÉPENDANCE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_formule1B_shannon_jointe_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - ShannonT (PRIORITÉ 5)
Entropie de Shannon Jointe théorique.
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    subsequence = sequence[1:n]
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    entropy = zero(T)
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

"""
    calculer_formule6B_divergence_kl_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - DivKLT (PRIORITÉ 2)
Divergence KL Observée vs Théorique.
"""
function calculer_formule6B_divergence_kl_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    subsequence = sequence[1:n]
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    divergence = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        p_obs = T(count) / n_total
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            divergence += p_obs * (log(p_obs / p_theo) / log(formulas.base))
        end
    end

    return divergence
end

"""
    calculer_formule8B_entropie_croisee_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - CrossT (PRIORITÉ 2)
Entropie Croisée Observée vs Théorique.
"""
function calculer_formule8B_entropie_croisee_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    subsequence = sequence[1:n]
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    cross_entropy = zero(T)
    n_total = T(length(subsequence))

    for (value, count) in counts
        p_obs = T(count) / n_total
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)

        if p_obs > zero(T) && p_theo > zero(T)
            cross_entropy -= p_obs * (log(p_theo) / log(formulas.base))
        end
    end

    return cross_entropy
end

"""
    calculer_formule9B_entropie_topologique_theo(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

MÉTRIQUE AUTONOME - TopoT (PRIORITÉ 3)
Entropie Topologique Multi-Échelles.
"""
function calculer_formule9B_entropie_topologique_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    subsequence = sequence[1:n]

    function calculer_entropie_blocs(block_size::Int)
        if block_size > length(subsequence)
            return zero(T)
        end

        blocs_distincts = Set{Vector{String}}()
        for i in 1:(length(subsequence) - block_size + 1)
            bloc = subsequence[i:i+block_size-1]
            push!(blocs_distincts, bloc)
        end

        entropy = zero(T)
        for bloc in blocs_distincts
            p_bloc_theo = one(T)
            for value in bloc
                p_value_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
                p_bloc_theo *= p_value_theo
            end

            if p_bloc_theo > zero(T)
                entropy -= p_bloc_theo * (log(p_bloc_theo) / log(formulas.base))
            end
        end

        return entropy
    end

    h_blocs_1 = calculer_entropie_blocs(1)
    h_blocs_2 = n >= 2 ? calculer_entropie_blocs(2) : zero(T)
    h_blocs_3 = n >= 3 ? calculer_entropie_blocs(3) : zero(T)

    w1, w2, w3 = T(0.167), T(0.333), T(0.500)
    return w1 * h_blocs_1 + w2 * h_blocs_2 + w3 * h_blocs_3
end

# ═══════════════════════════════════════════════════════════════════════════════
# MODULE 3: SUPPORT PROBABILITÉS (NIVEAU 2)
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_probabilite_conditionnelle_empirique(formulas::FormulasTheoretical{T}, current_value::String, previous_value::String) where T -> T

SUPPORT PROBABILITÉS - Niveau Base
Calcule la probabilité conditionnelle empirique.
"""
function calculer_probabilite_conditionnelle_empirique(
    formulas::FormulasTheoretical{T},
    current_value::String,
    previous_value::String
) where T<:AbstractFloat

    if !hasfield(typeof(formulas), :sequence_complete) || isempty(formulas.sequence_complete)
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end

    sequence_complete = formulas.sequence_complete
    count_transition = 0
    count_previous = 0

    for i in 1:(length(sequence_complete) - 1)
        if sequence_complete[i] == previous_value
            count_previous += 1
            if sequence_complete[i + 1] == current_value
                count_transition += 1
            end
        end
    end

    if count_previous > 0
        return T(count_transition) / T(count_previous)
    else
        return get(formulas.theoretical_probs, current_value, formulas.epsilon)
    end
end
